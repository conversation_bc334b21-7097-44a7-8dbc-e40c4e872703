"""
Enhanced Content Processor for Amazon Affiliate Integration

Provides async content processing with configurable concurrency, state management,
and improved shortcode generation with proper heading structure.
"""

import asyncio
import logging
import re
import time
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime

from ..core.config import (
    CONCURRENCY_LIMIT, PRODUCT_HEADING_TEMPLATE, SHORTCODE_TEMPLATE, 
    PRODUCT_SECTION_TEMPLATE, AAWP_TRACKING_ID
)
from ..core.models import (
    ArticleInfo, ArticleProcessingResult, ProcessingStatus, 
    ProductInfo, ShortcodeInfo
)
from ..clients.openai_client import OpenAIClient
from ..processors.state_manager import StateManager
from ..utils.backup_manager import BackupManager
from ..utils.helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorHandler, error_context

class ContentProcessor:
    """
    Enhanced content processor with async capabilities and state management
    
    Features:
    - Configurable concurrency control
    - State management integration
    - Enhanced shortcode format with items="1"
    - Proper heading structure
    - Comprehensive error handling and progress reporting
    """
    
    def __init__(self, 
                 openai_client: OpenAIClient,
                 state_manager: StateManager,
                 backup_manager: BackupManager,
                 concurrency_limit: int = CONCURRENCY_LIMIT):
        """
        Initialize ContentProcessor
        
        Args:
            openai_client: OpenAI client for product analysis
            state_manager: State manager for tracking processed URLs
            backup_manager: Backup manager for content backups
            concurrency_limit: Maximum concurrent processing operations
        """
        self.openai_client = openai_client
        self.state_manager = state_manager
        self.backup_manager = backup_manager
        self.concurrency_limit = concurrency_limit
        self.logger = logging.getLogger(__name__)
        
        # Create semaphore for concurrency control
        self.semaphore = asyncio.Semaphore(concurrency_limit)
        
        self.logger.info(f"ContentProcessor initialized with concurrency limit: {concurrency_limit}")
    
    def _create_product_heading(self) -> str:
        """
        Create the product recommendation heading
        
        Returns:
            Gutenberg heading block for product recommendations
        """
        return PRODUCT_HEADING_TEMPLATE
    
    def _create_aawp_shortcode(self, product_name: str) -> str:
        """
        Create AAWP shortcode for a product with items="1"
        
        Args:
            product_name: Name of the product
            
        Returns:
            AAWP shortcode as Gutenberg block with items="1"
        """
        return SHORTCODE_TEMPLATE.format(
            product=product_name,
            tracking_id=AAWP_TRACKING_ID
        )
    
    def _create_product_section(self, product_name: str) -> str:
        """
        Create complete product section with heading and shortcode
        
        Args:
            product_name: Name of the product
            
        Returns:
            Complete product section with heading and shortcode
        """
        return PRODUCT_SECTION_TEMPLATE.format(
            product=product_name,
            tracking_id=AAWP_TRACKING_ID
        )
    
    def _parse_h2_sections_from_content(self, content: str) -> List[Dict[str, str]]:
        """
        Parse H2 sections from Gutenberg content to extract headings and paragraphs
        
        Args:
            content: Gutenberg block formatted content
            
        Returns:
            List of sections with heading and second paragraph
        """
        sections = []
        
        # Split content into blocks
        blocks = re.split(r'\n\s*\n', content)
        
        current_heading = None
        paragraphs_after_heading = []
        
        for block in blocks:
            block = block.strip()
            if not block:
                continue
            
            # Check if this is an H2 heading block
            h2_match = re.search(r'<h2[^>]*class="wp-block-heading"[^>]*>(.*?)</h2>', block, re.DOTALL)
            if h2_match:
                # Save previous section if we have enough paragraphs
                if current_heading and len(paragraphs_after_heading) >= 2:
                    # Clean the second paragraph text
                    second_paragraph = re.sub(r'<[^>]+>', '', paragraphs_after_heading[1])
                    second_paragraph = re.sub(r'\s+', ' ', second_paragraph).strip()
                    
                    if len(second_paragraph) > 20:  # Ensure meaningful content
                        sections.append({
                            'heading': current_heading,
                            'second_paragraph': second_paragraph
                        })
                
                # Start new section
                current_heading = h2_match.group(1).strip()
                paragraphs_after_heading = []
                continue
            
            # Check if this is a paragraph block
            if current_heading and '<!-- wp:paragraph -->' in block:
                paragraph_match = re.search(r'<p[^>]*>(.*?)</p>', block, re.DOTALL)
                if paragraph_match:
                    paragraph_text = paragraph_match.group(1).strip()
                    if paragraph_text:  # Only add non-empty paragraphs
                        paragraphs_after_heading.append(paragraph_text)
        
        # Handle the last section
        if current_heading and len(paragraphs_after_heading) >= 2:
            second_paragraph = re.sub(r'<[^>]+>', '', paragraphs_after_heading[1])
            second_paragraph = re.sub(r'\s+', ' ', second_paragraph).strip()
            
            if len(second_paragraph) > 20:
                sections.append({
                    'heading': current_heading,
                    'second_paragraph': second_paragraph
                })
        
        self.logger.debug(f"Parsed {len(sections)} H2 sections from content")
        return sections
    
    def _insert_aawp_shortcodes_pattern_based(self, content: str, aawp_data: Dict[str, List[str]]) -> Tuple[str, List[ShortcodeInfo]]:
        """
        Insert AAWP shortcodes after first paragraph of each H2 section
        
        Args:
            content: Gutenberg block formatted content
            aawp_data: Dictionary mapping H2 heading text to list of product names
            
        Returns:
            Tuple of (modified content, list of shortcode info)
        """
        if not aawp_data:
            self.logger.info("No AAWP data provided, returning original content")
            return content, []
        
        modified_content = content
        shortcodes_added = []
        
        for heading_text, products in aawp_data.items():
            if not products:
                continue
            
            # Find the H2 heading in the content
            heading_pattern = rf'(<!-- wp:heading -->\s*<h2[^>]*class="wp-block-heading"[^>]*>{re.escape(heading_text)}</h2>\s*<!-- /wp:heading -->)'
            heading_match = re.search(heading_pattern, modified_content, re.DOTALL)
            
            if not heading_match:
                self.logger.warning(f"Could not find heading '{heading_text}' in content")
                continue
            
            # Find the first paragraph after this heading
            heading_end = heading_match.end()
            remaining_content = modified_content[heading_end:]
            
            # Look for the first paragraph block after the heading
            paragraph_pattern = r'(<!-- wp:paragraph -->\s*<p[^>]*>.*?</p>\s*<!-- /wp:paragraph -->)'
            paragraph_match = re.search(paragraph_pattern, remaining_content, re.DOTALL)
            
            if not paragraph_match:
                self.logger.warning(f"Could not find first paragraph after heading '{heading_text}'")
                continue
            
            # Calculate insertion point (after the first paragraph)
            insertion_point = heading_end + paragraph_match.end()
            
            # Use only the first product (items="1" approach)
            product = products[0]
            
            # Create product section with heading and shortcode
            product_section = f"\n\n{self._create_product_section(product)}\n"
            
            # Insert the product section
            modified_content = (
                modified_content[:insertion_point] + 
                product_section + 
                modified_content[insertion_point:]
            )
            
            # Track the shortcode info
            shortcode_info = ShortcodeInfo(
                product_name=product,
                heading=heading_text,
                position=insertion_point,
                template_used="product_section_with_heading"
            )
            shortcodes_added.append(shortcode_info)
            
            self.logger.debug(f"Inserted product section for '{product}' after heading '{heading_text}'")
        
        self.logger.info(f"Inserted {len(shortcodes_added)} product sections into content")
        return modified_content, shortcodes_added
    
    async def _generate_products_for_sections(self, sections: List[Dict[str, str]]) -> Dict[str, List[str]]:
        """
        Generate product recommendations for H2 sections using OpenAI batch processing

        Args:
            sections: List of sections with heading and paragraph content

        Returns:
            Dictionary mapping headings to product lists
        """
        if not sections:
            return {}

        self.logger.info(f"Generating products for {len(sections)} sections using batch processing")

        try:
            # Use batch processing for efficiency
            aawp_data = await self.openai_client.analyze_products_batch(sections)

            # Log results
            for heading, products in aawp_data.items():
                self.logger.info(f"Generated products for '{heading}': {products}")

            return aawp_data

        except Exception as e:
            self.logger.error(f"Batch processing failed, falling back to individual processing: {e}")

            # Fallback to individual processing if batch fails
            aawp_data = {}
            for section in sections:
                try:
                    products = await self.openai_client.analyze_products(
                        section['heading'],
                        section['second_paragraph']
                    )
                    aawp_data[section['heading']] = products
                    self.logger.info(f"Generated products for '{section['heading']}': {products}")
                except Exception as section_error:
                    self.logger.error(f"Error generating products for '{section['heading']}': {section_error}")
                    aawp_data[section['heading']] = ['Premium Home Decor Item', 'Luxury Interior Accessory']

            return aawp_data
    
    async def process_article_content(self, article: ArticleInfo) -> ArticleProcessingResult:
        """
        Process a single article with enhanced async capabilities
        
        Args:
            article: Article information
            
        Returns:
            Processing result with detailed information
        """
        start_time = time.time()
        
        async with self.semaphore:  # Concurrency control
            async with error_context(self.logger, f"processing article {article.id}", str(article.id)) as error_handler:
                try:
                    self.logger.info(f"Processing article {article.id}: {article.title}")
                    
                    # Create backup
                    backup_filename = await self.backup_manager.create_backup(article, "pre_processing")
                    backup_created = backup_filename is not None
                    
                    if not backup_created:
                        self.logger.warning(f"Failed to create backup for article {article.id}, continuing anyway")
                    
                    # Parse H2 sections
                    sections = self._parse_h2_sections_from_content(article.content)
                    
                    if not sections:
                        self.logger.warning(f"No H2 sections found in article {article.id}")
                        return ArticleProcessingResult(
                            article_id=article.id,
                            url=article.url,
                            domain=article.domain,
                            status=ProcessingStatus.SKIPPED,
                            processing_time=time.time() - start_time,
                            backup_created=backup_created,
                            error_message="No H2 sections found"
                        )
                    
                    # Generate products for sections
                    aawp_data = await self._generate_products_for_sections(sections)
                    
                    # Create product info objects
                    products_generated = []
                    for i, section in enumerate(sections):
                        heading = section['heading']
                        if heading in aawp_data and aawp_data[heading]:
                            product_info = ProductInfo(
                                name=aawp_data[heading][0],  # Use first product only
                                heading=heading,
                                section_index=i
                            )
                            products_generated.append(product_info)
                    
                    # Insert shortcodes
                    modified_content, shortcodes_added = self._insert_aawp_shortcodes_pattern_based(
                        article.content, aawp_data
                    )
                    
                    # Update article content (this would be done by the calling code)
                    processing_time = time.time() - start_time
                    
                    result = ArticleProcessingResult(
                        article_id=article.id,
                        url=article.url,
                        domain=article.domain,
                        status=ProcessingStatus.COMPLETED,
                        shortcodes_added=shortcodes_added,
                        products_generated=products_generated,
                        processing_time=processing_time,
                        backup_created=backup_created
                    )
                    
                    self.logger.info(
                        f"Successfully processed article {article.id} in {processing_time:.2f}s. "
                        f"Added {len(shortcodes_added)} shortcodes for {len(sections)} sections."
                    )
                    
                    return result
                    
                except Exception as e:
                    processing_time = time.time() - start_time
                    await error_handler.handle_error(e, f"processing article {article.id}", str(article.id))
                    
                    return ArticleProcessingResult(
                        article_id=article.id,
                        url=article.url,
                        domain=article.domain,
                        status=ProcessingStatus.FAILED,
                        processing_time=processing_time,
                        error_message=str(e)
                    )
    
    async def process_articles_batch(self, articles: List[ArticleInfo]) -> List[ArticleProcessingResult]:
        """
        Process multiple articles concurrently with progress reporting
        
        Args:
            articles: List of articles to process
            
        Returns:
            List of processing results
        """
        if not articles:
            return []
        
        self.logger.info(f"Starting batch processing of {len(articles)} articles with concurrency limit {self.concurrency_limit}")
        
        # Create progress reporter
        progress = ProgressReporter(len(articles), "Processing Articles")
        
        # Process articles concurrently
        tasks = []
        for article in articles:
            task = self.process_article_content(article)
            tasks.append(task)
        
        # Gather results with progress updates
        results = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)
            
            # Update progress
            if result.status == ProcessingStatus.COMPLETED:
                progress.update(completed=1)
            elif result.status == ProcessingStatus.FAILED:
                progress.update(failed=1)
            else:
                progress.update(skipped=1)
        
        progress.finish("Batch processing completed")
        
        # Log summary
        completed = sum(1 for r in results if r.status == ProcessingStatus.COMPLETED)
        failed = sum(1 for r in results if r.status == ProcessingStatus.FAILED)
        skipped = sum(1 for r in results if r.status == ProcessingStatus.SKIPPED)
        
        self.logger.info(
            f"Batch processing completed: {completed} successful, {failed} failed, {skipped} skipped"
        )
        
        return results
