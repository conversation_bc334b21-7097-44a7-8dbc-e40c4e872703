"""
Enhanced Orchestrator for Amazon Affiliate Integration

Main orchestration class that coordinates all components with state management,
force parameter support, and comprehensive workflow automation.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Any
from datetime import datetime

from ..core.config import (
    CONCURRENCY_LIMIT, WP_CREDENTIALS, get_configured_domains,
    validate_configuration
)
from ..core.models import (
    ArticleInfo, ArticleProcessingResult, ProcessingWorkflowResult,
    ProcessingStatus, ProcessingState, VisibleShortcode, CrawlerResult, CrawlerStatus
)
from ..clients.openai_client import OpenAIClient
from ..clients.wordpress_client import WordPressClient
from ..processors.content_processor import ContentProcessor
from ..processors.cleanup_processor import CleanupProcessor
from ..processors.state_manager import StateManager
from ..crawlers.shortcode_crawler import ShortcodeCrawler
from ..crawlers.browser_shortcode_crawler import BrowserShortcodeCrawler
from ..utils.backup_manager import BackupManager
from ..utils.helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, setup_logging

class EnhancedOrchestrator:
    """
    Enhanced orchestrator with state management and force parameter support
    
    Features:
    - State management with processed URL tracking
    - Force parameter to bypass processed URL checks
    - Async processing with configurable concurrency
    - Comprehensive error handling and progress reporting
    - Automatic backup and git checkpoint creation
    - Domain-specific and cross-domain processing
    """
    
    def __init__(self, concurrency_limit: int = CONCURRENCY_LIMIT):
        """
        Initialize the enhanced orchestrator
        
        Args:
            concurrency_limit: Maximum concurrent processing operations
        """
        self.concurrency_limit = concurrency_limit
        self.logger = setup_logging()
        
        # Initialize core components
        self.openai_client = OpenAIClient()
        self.state_manager = StateManager()
        self.backup_manager = BackupManager()
        self.error_handler = ErrorHandler(self.logger)
        
        # Initialize content processor
        self.content_processor = ContentProcessor(
            openai_client=self.openai_client,
            state_manager=self.state_manager,
            backup_manager=self.backup_manager,
            concurrency_limit=concurrency_limit
        )
        
        # WordPress clients cache
        self._wp_clients: Dict[str, WordPressClient] = {}
        
        self.logger.info(f"Enhanced orchestrator initialized with concurrency limit: {concurrency_limit}")
    
    def _get_wordpress_client(self, domain: str) -> WordPressClient:
        """
        Get or create WordPress client for domain
        
        Args:
            domain: Domain name
            
        Returns:
            WordPress client instance
        """
        if domain not in self._wp_clients:
            if domain not in WP_CREDENTIALS or not WP_CREDENTIALS[domain]:
                raise ValueError(f"No credentials configured for domain: {domain}")
            
            self._wp_clients[domain] = WordPressClient(domain, WP_CREDENTIALS[domain])
        
        return self._wp_clients[domain]
    
    async def _load_processing_state(self, force: bool = False) -> ProcessingState:
        """
        Load current processing state
        
        Args:
            force: If True, ignore processed URLs and process all
            
        Returns:
            Current processing state
        """
        if force:
            # Force mode: empty processed URLs set
            processed_urls = set()
            self.logger.info("Force mode enabled: ignoring previously processed URLs")
        else:
            # Normal mode: load processed URLs
            processed_urls = await self.state_manager.load_processed_urls()
            self.logger.info(f"Loaded {len(processed_urls)} previously processed URLs")
        
        excluded_urls = await self.state_manager.load_excluded_urls()
        self.logger.info(f"Loaded {len(excluded_urls)} excluded URLs")
        
        return ProcessingState(
            processed_urls=processed_urls,
            excluded_urls=excluded_urls
        )
    
    async def _filter_articles_by_state(self,
                                       articles: List[ArticleInfo],
                                       state: ProcessingState,
                                       force: bool = False) -> List[ArticleInfo]:
        """
        Filter articles based on processing state

        Args:
            articles: List of articles to filter
            state: Current processing state
            force: If True, ignore processed URLs and process all

        Returns:
            Filtered list of articles to process
        """
        if force:
            # Force mode: process all articles except excluded
            filtered = [article for article in articles if article.url not in state.excluded_urls]
            self.logger.info(f"Force mode: processing {len(filtered)} articles (excluding {len(articles) - len(filtered)} excluded)")
        else:
            # Normal mode: skip processed and excluded
            filtered = [
                article for article in articles 
                if article.url not in state.processed_urls and article.url not in state.excluded_urls
            ]
            skipped_processed = len([a for a in articles if a.url in state.processed_urls])
            skipped_excluded = len([a for a in articles if a.url in state.excluded_urls])
            
            self.logger.info(
                f"Normal mode: processing {len(filtered)} articles "
                f"(skipping {skipped_processed} processed, {skipped_excluded} excluded)"
            )
        
        return filtered
    
    async def _update_processing_state(self, 
                                     results: List[ArticleProcessingResult], 
                                     state: ProcessingState) -> bool:
        """
        Update processing state with results
        
        Args:
            results: Processing results
            state: Current processing state
            
        Returns:
            True if state was updated successfully
        """
        # Collect successfully processed URLs
        new_processed_urls = set()
        for result in results:
            if result.status == ProcessingStatus.COMPLETED:
                new_processed_urls.add(result.url)
        
        if new_processed_urls:
            # Update processed URLs
            updated_processed = state.processed_urls.union(new_processed_urls)
            success = await self.state_manager.save_processed_urls(updated_processed)
            
            if success:
                self.logger.info(f"Updated state with {len(new_processed_urls)} newly processed URLs")
                return True
            else:
                self.logger.error("Failed to update processing state")
                return False
        
        return True  # No updates needed
    
    async def process_domain(self, 
                           domain: str, 
                           force: bool = False,
                           dry_run: bool = False,
                           limit: Optional[int] = None) -> ProcessingWorkflowResult:
        """
        Process all articles for a specific domain
        
        Args:
            domain: Domain to process
            force: If True, process all articles regardless of previous processing
            dry_run: If True, don't actually update articles
            limit: Maximum number of articles to process (for testing)
            
        Returns:
            Workflow result with comprehensive statistics
        """
        start_time = time.time()
        
        self.logger.info(f"Starting domain processing: {domain} (force={force}, dry_run={dry_run}, limit={limit})")
        
        try:
            # Validate domain
            if domain not in get_configured_domains():
                raise ValueError(f"Domain {domain} is not configured or has no credentials")
            
            # Load processing state
            state = await self._load_processing_state(force=force)
            
            # Get WordPress client
            wp_client = self._get_wordpress_client(domain)
            
            # Test connection
            if not await wp_client.test_connection():
                raise ConnectionError(f"Failed to connect to WordPress API for {domain}")
            
            # Fetch articles
            self.logger.info(f"Fetching articles from {domain}...")
            wp_articles = await wp_client.fetch_articles_since_january_2025(limit=limit)
            
            if not wp_articles:
                self.logger.info(f"No articles found for {domain}")
                return ProcessingWorkflowResult(
                    domain=domain,
                    total_articles=0,
                    articles_processed=0,
                    articles_failed=0,
                    articles_skipped=0,
                    processing_time=time.time() - start_time,
                    force_mode=force,
                    dry_run=dry_run
                )
            
            # Convert to ArticleInfo objects
            articles = wp_client.articles_to_article_info(wp_articles)
            self.logger.info(f"Converted {len(articles)} articles to ArticleInfo objects")
            
            # Filter articles based on state
            articles_to_process = await self._filter_articles_by_state(articles, state, force)
            
            if not articles_to_process:
                self.logger.info(f"No articles to process for {domain} after filtering")
                return ProcessingWorkflowResult(
                    domain=domain,
                    total_articles=len(articles),
                    articles_processed=0,
                    articles_failed=0,
                    articles_skipped=len(articles),
                    processing_time=time.time() - start_time,
                    force_mode=force,
                    dry_run=dry_run
                )
            
            # Create git checkpoint before processing
            checkpoint_created = await self.backup_manager.create_git_checkpoint(
                f"Pre-processing checkpoint for {domain} ({len(articles_to_process)} articles)"
            )
            
            if checkpoint_created:
                self.logger.info(f"Created git checkpoint before processing {domain}")
            
            # Process articles
            self.logger.info(f"Processing {len(articles_to_process)} articles for {domain}...")
            processing_results = await self.content_processor.process_articles_batch(articles_to_process)
            
            # Update WordPress if not dry run
            update_results = []
            if not dry_run:
                self.logger.info(f"Updating WordPress content for {domain}...")
                
                # Prepare updates for successful processing results
                updates = []
                for i, result in enumerate(processing_results):
                    if result.status == ProcessingStatus.COMPLETED:
                        # Get the modified content from the article
                        article = articles_to_process[i]
                        updates.append({
                            'id': result.article_id,
                            'content': article.content,  # This would contain the modified content
                            'title': article.title
                        })
                
                if updates:
                    update_result = await wp_client.update_articles_batch(updates)
                    update_results = update_result['results']
                    
                    self.logger.info(
                        f"WordPress updates completed: {update_result['success']} successful, "
                        f"{update_result['failed']} failed"
                    )
            
            # Update processing state
            if not dry_run:
                await self._update_processing_state(processing_results, state)
            
            # Create final git checkpoint
            if not dry_run and checkpoint_created:
                await self.backup_manager.create_git_checkpoint(
                    f"Post-processing checkpoint for {domain} "
                    f"({len([r for r in processing_results if r.status == ProcessingStatus.COMPLETED])} articles processed)"
                )
            
            # Calculate statistics
            completed = len([r for r in processing_results if r.status == ProcessingStatus.COMPLETED])
            failed = len([r for r in processing_results if r.status == ProcessingStatus.FAILED])
            skipped = len([r for r in processing_results if r.status == ProcessingStatus.SKIPPED])
            skipped += len(articles) - len(articles_to_process)  # Add filtered articles
            
            processing_time = time.time() - start_time
            
            # Create workflow result
            workflow_result = ProcessingWorkflowResult(
                domain=domain,
                total_articles=len(articles),
                articles_processed=completed,
                articles_failed=failed,
                articles_skipped=skipped,
                processing_results=processing_results,
                update_results=update_results,
                processing_time=processing_time,
                force_mode=force,
                dry_run=dry_run,
                git_checkpoint_created=checkpoint_created
            )
            
            self.logger.info(
                f"Domain processing completed for {domain}: "
                f"{completed} processed, {failed} failed, {skipped} skipped "
                f"in {processing_time:.2f}s"
            )
            
            return workflow_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Error processing domain {domain}: {e}")
            
            return ProcessingWorkflowResult(
                domain=domain,
                total_articles=0,
                articles_processed=0,
                articles_failed=0,
                articles_skipped=0,
                processing_time=processing_time,
                force_mode=force,
                dry_run=dry_run,
                error_message=str(e)
            )
    
    async def process_all_domains(self, 
                                force: bool = False,
                                dry_run: bool = False,
                                limit: Optional[int] = None) -> List[ProcessingWorkflowResult]:
        """
        Process all configured domains
        
        Args:
            force: If True, process all articles regardless of previous processing
            dry_run: If True, don't actually update articles
            limit: Maximum number of articles to process per domain (for testing)
            
        Returns:
            List of workflow results for each domain
        """
        configured_domains = get_configured_domains()
        
        if not configured_domains:
            self.logger.error("No domains configured with valid credentials")
            return []
        
        self.logger.info(
            f"Starting processing for {len(configured_domains)} domains: {', '.join(configured_domains)}"
        )
        
        # Process domains sequentially to avoid overwhelming APIs
        results = []
        for domain in configured_domains:
            try:
                result = await self.process_domain(domain, force=force, dry_run=dry_run, limit=limit)
                results.append(result)
                
                # Small delay between domains
                await asyncio.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"Error processing domain {domain}: {e}")
                # Create error result
                error_result = ProcessingWorkflowResult(
                    domain=domain,
                    total_articles=0,
                    articles_processed=0,
                    articles_failed=0,
                    articles_skipped=0,
                    processing_time=0.0,
                    force_mode=force,
                    dry_run=dry_run,
                    error_message=str(e)
                )
                results.append(error_result)
        
        # Log summary
        total_processed = sum(r.articles_processed for r in results)
        total_failed = sum(r.articles_failed for r in results)
        total_skipped = sum(r.articles_skipped for r in results)
        total_time = sum(r.processing_time for r in results)
        
        self.logger.info(
            f"All domains processing completed: "
            f"{total_processed} processed, {total_failed} failed, {total_skipped} skipped "
            f"across {len(configured_domains)} domains in {total_time:.2f}s"
        )
        
        return results

    async def complete_workflow(self, domain: str = None, force: bool = False,
                              dry_run: bool = False, limit: Optional[int] = None,
                              crawler_method: str = 'http') -> Dict[str, Any]:
        """
        Execute the complete workflow: Process Articles → Crawl → Cleanup

        This is the full end-to-end workflow that:
        1. Processes articles to add Amazon shortcodes
        2. Crawls the modified articles to detect failed shortcodes
        3. Cleans up any visible (failed) shortcodes

        Args:
            domain: Specific domain to process (None for all domains)
            force: Force reprocessing of previously processed articles
            dry_run: Perform dry run without actually updating articles
            limit: Maximum number of articles to process
            crawler_method: Crawling method to use

        Returns:
            Complete workflow results dictionary
        """
        self.logger.info("🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup")

        workflow_start = time.time()
        results = {
            'processing_results': {},
            'crawler_results': [],
            'cleanup_results': [],
            'statistics': {},
            'success': False,
            'total_processing_time': 0.0
        }

        try:
            # Phase 1: Process Articles (Add Shortcodes)
            self.logger.info("📝 Phase 1: Processing articles to add Amazon shortcodes...")

            if domain:
                processing_results = await self.process_domain(
                    domain, force=force, dry_run=dry_run, limit=limit
                )
                results['processing_results'] = processing_results.to_dict()
                processing_success = processing_results.success
            else:
                processing_results_list = await self.process_all_domains(
                    force=force, dry_run=dry_run, limit=limit
                )
                results['processing_results'] = [r.to_dict() for r in processing_results_list]
                processing_success = all(r.success for r in processing_results_list)

            if not processing_success:
                self.logger.error("❌ Phase 1 failed - aborting complete workflow")
                results['success'] = False
                return results

            self.logger.info("✅ Phase 1 completed successfully")

            # Phase 2 & 3: Crawl and Cleanup (only if processing succeeded and not dry run)
            if not dry_run:
                self.logger.info("🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...")

                crawl_cleanup_results = await self.crawl_and_cleanup_workflow(domain=domain, crawler_method=crawler_method)

                results['crawler_results'] = crawl_cleanup_results.get('crawler_results', [])
                results['cleanup_results'] = crawl_cleanup_results.get('cleanup_results', [])

                if not crawl_cleanup_results.get('success', False):
                    self.logger.warning("⚠️ Phase 2 & 3 had issues, but processing was successful")
                else:
                    self.logger.info("✅ Phase 2 & 3 completed successfully")
            else:
                self.logger.info("⏭️ Skipping crawl and cleanup phases (dry run mode)")

            # Compile complete statistics
            results['statistics'] = self._compile_complete_workflow_statistics(
                results['processing_results'],
                results.get('crawler_results', []),
                results.get('cleanup_results', [])
            )

            results['success'] = True
            results['total_processing_time'] = time.time() - workflow_start

            self.logger.info(
                f"🎉 COMPLETE workflow finished successfully in {results['total_processing_time']:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"❌ Complete workflow failed: {e}")
            results['success'] = False
            results['error'] = str(e)
            results['total_processing_time'] = time.time() - workflow_start

        return results

    def _compile_complete_workflow_statistics(self, processing_results: Dict[str, Any],
                                            crawler_results: List[Dict[str, Any]],
                                            cleanup_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compile statistics for the complete workflow"""

        # Processing statistics
        processing_stats = processing_results.get('statistics', {})

        # Crawler statistics
        total_crawled = len(crawler_results)
        total_visible_shortcodes = sum(
            len(result.get('visible_shortcodes', [])) for result in crawler_results
        )

        # Cleanup statistics
        total_cleaned = len(cleanup_results)
        total_shortcodes_removed = sum(
            len(result.get('actions', [])) for result in cleanup_results
        )

        return {
            'processing': processing_stats,
            'crawling': {
                'articles_crawled': total_crawled,
                'visible_shortcodes_found': total_visible_shortcodes,
                'articles_with_issues': len([r for r in crawler_results if r.get('visible_shortcodes')])
            },
            'cleanup': {
                'articles_cleaned': total_cleaned,
                'shortcodes_removed': total_shortcodes_removed,
                'cleanup_success_rate': (total_cleaned / max(total_visible_shortcodes, 1)) * 100
            }
        }

    async def crawl_and_cleanup_workflow(self, domain: str = None, crawl_only: bool = False,
                                       cleanup_only: bool = False, crawler_method: str = 'http') -> Dict[str, Any]:
        """
        Execute the complete crawl and cleanup workflow

        Args:
            domain: Specific domain to process (None for all domains)
            crawl_only: Only perform crawling, skip cleanup
            cleanup_only: Only perform cleanup based on existing missing products data
            crawler_method: Crawling method ('http', 'browser', or 'hybrid')

        Returns:
            Workflow results dictionary
        """
        self.logger.info("Starting crawl and cleanup workflow...")

        workflow_start = time.time()
        results = {
            'crawler_results': [],
            'cleanup_results': [],
            'statistics': {},
            'success': False,
            'processing_time': 0.0
        }

        try:
            # Determine domains to process
            domains_to_process = [domain] if domain else get_configured_domains()

            if not cleanup_only:
                # Phase 1: Crawl articles for visible shortcodes
                self.logger.info("Phase 1: Crawling articles for visible shortcodes...")

                all_crawler_results = []
                for domain_name in domains_to_process:
                    domain_results = await self._crawl_domain_articles(domain_name, crawler_method)
                    all_crawler_results.extend(domain_results)

                # Update missing products state
                await self._update_missing_products_state(all_crawler_results)

                # Convert to dictionaries for storage
                results['crawler_results'] = [result.to_dict() for result in all_crawler_results]

            if not crawl_only:
                # Phase 2: Cleanup failed shortcodes
                self.logger.info("Phase 2: Cleaning up failed shortcodes...")

                if cleanup_only:
                    # Load existing missing products data for cleanup
                    all_crawler_results = await self._load_missing_products_for_cleanup()
                else:
                    # Convert back from dict to CrawlerResult objects for cleanup
                    all_crawler_results = self._convert_dicts_to_crawler_results(results['crawler_results'])

                cleanup_results = await self._cleanup_failed_shortcodes(all_crawler_results)
                results['cleanup_results'] = cleanup_results

            # Compile statistics
            results['statistics'] = self._compile_workflow_statistics(
                results.get('crawler_results', []),
                results.get('cleanup_results', [])
            )

            results['success'] = True
            results['processing_time'] = time.time() - workflow_start

            self.logger.info(
                f"Crawl and cleanup workflow completed successfully in {results['processing_time']:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"Crawl and cleanup workflow failed: {e}")
            results['error'] = str(e)
            results['processing_time'] = time.time() - workflow_start

        return results

    def _convert_dicts_to_crawler_results(self, result_dicts: List[Dict[str, Any]]) -> List:
        """
        Convert dictionary representations back to CrawlerResult objects

        Args:
            result_dicts: List of crawler result dictionaries

        Returns:
            List of CrawlerResult objects
        """
        from datetime import datetime

        crawler_results = []
        for result_dict in result_dicts:
            # Convert visible shortcodes
            visible_shortcodes = []
            for sc_dict in result_dict.get('visible_shortcodes', []):
                visible_shortcodes.append(VisibleShortcode(
                    product_name=sc_dict.get('product_name', ''),
                    heading=sc_dict.get('heading', ''),
                    full_shortcode=sc_dict.get('full_shortcode', ''),
                    position_in_content=sc_dict.get('position_in_content', 0)
                ))

            # Convert status
            status_value = result_dict.get('status', 'CRAWLED')
            status = CrawlerStatus.CRAWLED if status_value == 'CRAWLED' else CrawlerStatus.FAILED

            # Convert crawled_at
            crawled_at_str = result_dict.get('crawled_at')
            crawled_at = datetime.fromisoformat(crawled_at_str) if crawled_at_str else datetime.now()

            crawler_result = CrawlerResult(
                url=result_dict.get('url', ''),
                domain=result_dict.get('domain', ''),
                status=status,
                visible_shortcodes=visible_shortcodes,
                total_shortcodes_found=result_dict.get('total_shortcodes_found', 0),
                response_time=result_dict.get('response_time', 0.0),
                error_message=result_dict.get('error_message'),
                crawled_at=crawled_at
            )

            crawler_results.append(crawler_result)

        return crawler_results

    async def _crawl_domain_articles(self, domain: str, crawler_method: str = 'http') -> List:
        """
        Crawl articles for a specific domain

        Args:
            domain: Domain to crawl
            crawler_method: Crawling method ('http', 'browser', or 'hybrid')

        Returns:
            List of crawler results
        """
        try:
            # Get processed articles for this domain
            processed_urls = await self.state_manager.load_processed_urls()
            domain_processed_urls = [url for url in processed_urls if domain in url]

            if not domain_processed_urls:
                self.logger.info(f"No processed articles found for domain {domain}")
                return []

            # Convert URLs to ArticleInfo objects (simplified)
            articles = []
            for url in domain_processed_urls:
                articles.append(ArticleInfo(
                    id=0,  # Will be populated if needed
                    title="",
                    url=url,
                    content="",
                    date="",
                    modified="",
                    slug="",
                    domain=domain
                ))

            # Crawl articles using selected method
            if crawler_method == 'browser':
                self.logger.info(f"Using browser crawler for {domain}")
                async with BrowserShortcodeCrawler(concurrency_limit=self.concurrency_limit) as crawler:
                    crawler_results = await crawler.crawl_articles(articles)
            elif crawler_method == 'hybrid':
                self.logger.info(f"Using hybrid crawler for {domain} (HTTP first, browser fallback)")
                # Try HTTP first
                async with ShortcodeCrawler(concurrency_limit=self.concurrency_limit) as http_crawler:
                    http_results = await http_crawler.crawl_articles(articles)

                # Check if any failed or found no shortcodes, retry with browser
                failed_articles = [
                    article for i, article in enumerate(articles)
                    if not http_results[i].success or http_results[i].total_shortcodes_found == 0
                ]

                if failed_articles:
                    self.logger.info(f"Retrying {len(failed_articles)} articles with browser crawler")
                    async with BrowserShortcodeCrawler(concurrency_limit=self.concurrency_limit) as browser_crawler:
                        browser_results = await browser_crawler.crawl_articles(failed_articles)

                    # Merge results (replace failed HTTP results with browser results)
                    browser_result_map = {result.url: result for result in browser_results}
                    crawler_results = []
                    for i, result in enumerate(http_results):
                        if result.url in browser_result_map:
                            crawler_results.append(browser_result_map[result.url])
                        else:
                            crawler_results.append(result)
                else:
                    crawler_results = http_results
            else:  # Default to HTTP
                self.logger.info(f"Using HTTP crawler for {domain}")
                async with ShortcodeCrawler(concurrency_limit=self.concurrency_limit) as crawler:
                    crawler_results = await crawler.crawl_articles(articles)

            self.logger.info(
                f"Crawled {len(articles)} articles for domain {domain}, "
                f"found {sum(len(r.visible_shortcodes) for r in crawler_results)} visible shortcodes"
            )

            return crawler_results

        except Exception as e:
            self.logger.error(f"Error crawling domain {domain}: {e}")
            return []

    async def _update_missing_products_state(self, crawler_results: List) -> None:
        """
        Update missing products state based on crawler results

        Args:
            crawler_results: Results from crawling operation
        """
        for result in crawler_results:
            # Handle both CrawlerResult objects and dictionaries
            if isinstance(result, dict):
                success = result.get('success', False)
                has_visible_shortcodes = result.get('has_visible_shortcodes', False)
                url = result.get('url', '')
                visible_shortcodes = result.get('visible_shortcodes', [])
            else:
                success = result.success
                has_visible_shortcodes = result.has_visible_shortcodes
                url = result.url
                visible_shortcodes = result.visible_shortcodes

            if success and has_visible_shortcodes:
                await self.state_manager.track_missing_products(url, visible_shortcodes)

    async def _cleanup_failed_shortcodes(self, crawler_results: List) -> List:
        """
        Clean up failed shortcodes based on crawler results

        Args:
            crawler_results: Results from crawling operation

        Returns:
            List of cleanup results
        """
        # Group results by domain for efficient processing
        domain_results = {}
        for result in crawler_results:
            if result.success and result.has_visible_shortcodes:
                if result.domain not in domain_results:
                    domain_results[result.domain] = []
                domain_results[result.domain].append(result)

        all_cleanup_results = []

        for domain, results in domain_results.items():
            try:
                wp_client = self._get_wordpress_client(domain)
                cleanup_processor = CleanupProcessor(
                    wp_client,
                    self.state_manager,
                    self.concurrency_limit
                )

                domain_cleanup_results = await cleanup_processor.cleanup_failed_shortcodes(results)
                all_cleanup_results.extend(domain_cleanup_results)

            except Exception as e:
                self.logger.error(f"Error cleaning up domain {domain}: {e}")

        return all_cleanup_results

    async def _load_missing_products_for_cleanup(self) -> List:
        """
        Load missing products data and convert to crawler results for cleanup

        Returns:
            List of mock crawler results for cleanup processing
        """
        missing_products = await self.state_manager.load_missing_products()
        crawler_results = []

        for url, data in missing_products.items():
            # Extract domain from URL
            domain = url.split('/')[2] if '/' in url else 'unknown'

            # Convert missing products to visible shortcodes
            visible_shortcodes = []
            for heading, heading_data in data.get('headings', {}).items():
                for product_name in heading_data.get('missing_products', []):
                    visible_shortcodes.append(VisibleShortcode(
                        product_name=product_name,
                        heading=heading,
                        full_shortcode=f'[amazon bestseller="{product_name}" items="1"]',
                        position_in_content=0
                    ))

            if visible_shortcodes:
                from ..core.models import CrawlerResult, CrawlerStatus
                crawler_result = CrawlerResult(
                    url=url,
                    domain=domain,
                    status=CrawlerStatus.CRAWLED,
                    visible_shortcodes=visible_shortcodes,
                    total_shortcodes_found=len(visible_shortcodes)
                )
                crawler_results.append(crawler_result)

        return crawler_results

    def _compile_workflow_statistics(self, crawler_results: List, cleanup_results: List) -> Dict:
        """
        Compile comprehensive workflow statistics

        Args:
            crawler_results: Results from crawling phase
            cleanup_results: Results from cleanup phase

        Returns:
            Statistics dictionary
        """
        # Handle both CrawlerResult objects and dictionaries
        def get_success(r):
            return r.success if hasattr(r, 'success') else r.get('success', False)

        def get_visible_shortcodes(r):
            return r.visible_shortcodes if hasattr(r, 'visible_shortcodes') else r.get('visible_shortcodes', [])

        def get_has_visible_shortcodes(r):
            return r.has_visible_shortcodes if hasattr(r, 'has_visible_shortcodes') else r.get('has_visible_shortcodes', False)

        def get_actions_performed(r):
            return r.actions_performed if hasattr(r, 'actions_performed') else r.get('actions_performed', [])

        stats = {
            'crawler': {
                'articles_crawled': len(crawler_results),
                'successful_crawls': sum(1 for r in crawler_results if get_success(r)),
                'failed_crawls': sum(1 for r in crawler_results if not get_success(r)),
                'visible_shortcodes_found': sum(len(get_visible_shortcodes(r)) for r in crawler_results),
                'articles_with_issues': sum(1 for r in crawler_results if get_has_visible_shortcodes(r))
            },
            'cleanup': {
                'articles_processed': len(cleanup_results),
                'successful_cleanups': sum(1 for r in cleanup_results if get_success(r)),
                'failed_cleanups': sum(1 for r in cleanup_results if not get_success(r)),
                'shortcodes_removed': sum(len(get_actions_performed(r)) for r in cleanup_results)
            }
        }

        return stats

    async def validate_system(self) -> Dict[str, Any]:
        """
        Validate system configuration and connectivity
        
        Returns:
            Validation results dictionary
        """
        self.logger.info("Starting system validation...")
        
        # Validate configuration
        config_validation = validate_configuration()
        
        # Test OpenAI connection
        openai_test = await self.openai_client.test_connection()
        
        # Test WordPress connections
        wp_tests = {}
        for domain in get_configured_domains():
            try:
                wp_client = self._get_wordpress_client(domain)
                wp_tests[domain] = await wp_client.test_connection()
            except Exception as e:
                wp_tests[domain] = False
                self.logger.error(f"WordPress connection test failed for {domain}: {e}")
        
        # Test state manager
        state_test = await self.state_manager.test_operations()
        
        validation_result = {
            'configuration': config_validation,
            'openai_connection': openai_test,
            'wordpress_connections': wp_tests,
            'state_manager': state_test,
            'overall_status': (
                config_validation['valid'] and 
                openai_test and 
                all(wp_tests.values()) and 
                state_test
            )
        }
        
        if validation_result['overall_status']:
            self.logger.info("System validation passed")
        else:
            self.logger.warning("System validation failed - check individual components")
        
        return validation_result
