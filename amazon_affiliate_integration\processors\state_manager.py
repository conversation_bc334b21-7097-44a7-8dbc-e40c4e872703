"""
State Manager for Amazon Affiliate Integration

Handles persistent state management through JSON files with atomic operations,
backup creation, and data integrity validation.
"""

import json
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
import aiofiles
from contextlib import asynccontextmanager

from ..core.config import STATE_FILES
from ..core.models import ProcessingState, VisibleShortcode

class StateManager:
    """
    Manages persistent state for the Amazon Affiliate Integration system
    
    Handles:
    - Processed URLs tracking
    - Excluded URLs management
    - Missing products tracking
    - Atomic file operations
    - State validation and recovery
    """
    
    def __init__(self, state_dir: Path = None):
        """
        Initialize StateManager
        
        Args:
            state_dir: Directory to store state files (default: current directory)
        """
        self.state_dir = state_dir or Path.cwd()
        self.logger = logging.getLogger(__name__)
        self._locks = {}  # File locks for atomic operations
        
        # Initialize locks for each state file
        for file_key in STATE_FILES.keys():
            self._locks[file_key] = asyncio.Lock()
    
    def _get_file_path(self, file_key: str) -> Path:
        """Get full path for a state file"""
        filename = STATE_FILES.get(file_key)
        if not filename:
            raise ValueError(f"Unknown state file key: {file_key}")
        return self.state_dir / filename
    
    async def _read_json_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Read JSON file with error handling
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Dictionary data or empty dict if file doesn't exist
        """
        try:
            if not file_path.exists():
                return {}
            
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return json.loads(content) if content.strip() else {}
                
        except (json.JSONDecodeError, FileNotFoundError) as e:
            self.logger.warning(f"Error reading {file_path}: {e}. Using empty data.")
            return {}
        except Exception as e:
            self.logger.error(f"Unexpected error reading {file_path}: {e}")
            return {}
    
    async def _write_json_file(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """
        Write JSON file atomically with backup
        
        Args:
            file_path: Path to JSON file
            data: Data to write
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create backup if file exists
            if file_path.exists():
                backup_path = file_path.with_suffix(f'.bak.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
                await asyncio.to_thread(file_path.rename, backup_path)
                self.logger.debug(f"Created backup: {backup_path}")
            
            # Write to temporary file first
            temp_path = file_path.with_suffix('.tmp')
            async with aiofiles.open(temp_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, indent=2, ensure_ascii=False, default=str))
            
            # Atomic move
            await asyncio.to_thread(temp_path.rename, file_path)
            self.logger.debug(f"Successfully wrote {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing {file_path}: {e}")
            # Clean up temp file if it exists
            if temp_path.exists():
                try:
                    await asyncio.to_thread(temp_path.unlink)
                except:
                    pass
            return False
    
    @asynccontextmanager
    async def _file_lock(self, file_key: str):
        """Context manager for file locking"""
        lock = self._locks.get(file_key)
        if not lock:
            raise ValueError(f"No lock available for file key: {file_key}")
        
        async with lock:
            yield
    
    # =============================================================================
    # PROCESSED URLS MANAGEMENT
    # =============================================================================
    
    async def load_processed_urls(self) -> Set[str]:
        """
        Load processed URLs from state file
        
        Returns:
            Set of processed URLs
        """
        async with self._file_lock('processed_urls'):
            file_path = self._get_file_path('processed_urls')
            data = await self._read_json_file(file_path)
            return set(data.get('urls', []))
    
    async def save_processed_urls(self, urls: Set[str]) -> bool:
        """
        Save processed URLs to state file
        
        Args:
            urls: Set of processed URLs
            
        Returns:
            True if successful
        """
        async with self._file_lock('processed_urls'):
            file_path = self._get_file_path('processed_urls')
            data = {
                'urls': list(urls),
                'last_updated': datetime.now().isoformat(),
                'total_processed': len(urls)
            }
            return await self._write_json_file(file_path, data)
    
    async def add_processed_url(self, url: str) -> bool:
        """
        Add a single URL to processed list
        
        Args:
            url: URL to add
            
        Returns:
            True if successful
        """
        urls = await self.load_processed_urls()
        urls.add(url)
        return await self.save_processed_urls(urls)
    
    async def is_processed(self, url: str) -> bool:
        """
        Check if URL has been processed
        
        Args:
            url: URL to check
            
        Returns:
            True if URL has been processed
        """
        processed_urls = await self.load_processed_urls()
        return url in processed_urls
    
    # =============================================================================
    # EXCLUDED URLS MANAGEMENT
    # =============================================================================
    
    async def load_excluded_urls(self) -> Set[str]:
        """
        Load excluded URLs from state file
        
        Returns:
            Set of excluded URLs
        """
        async with self._file_lock('excluded_urls'):
            file_path = self._get_file_path('excluded_urls')
            data = await self._read_json_file(file_path)
            return set(data.get('urls', []))
    
    async def save_excluded_urls(self, urls: Set[str]) -> bool:
        """
        Save excluded URLs to state file
        
        Args:
            urls: Set of excluded URLs
            
        Returns:
            True if successful
        """
        async with self._file_lock('excluded_urls'):
            file_path = self._get_file_path('excluded_urls')
            data = {
                'urls': list(urls),
                'last_updated': datetime.now().isoformat()
            }
            return await self._write_json_file(file_path, data)
    
    async def add_excluded_url(self, url: str) -> bool:
        """
        Add a single URL to excluded list
        
        Args:
            url: URL to add
            
        Returns:
            True if successful
        """
        urls = await self.load_excluded_urls()
        urls.add(url)
        return await self.save_excluded_urls(urls)
    
    async def is_excluded(self, url: str) -> bool:
        """
        Check if URL is excluded from processing
        
        Args:
            url: URL to check
            
        Returns:
            True if URL is excluded
        """
        excluded_urls = await self.load_excluded_urls()
        return url in excluded_urls
    
    # =============================================================================
    # MISSING PRODUCTS MANAGEMENT
    # =============================================================================
    
    async def load_missing_products(self) -> Dict[str, Dict]:
        """
        Load missing products data from state file
        
        Returns:
            Dictionary mapping URLs to missing product information
        """
        async with self._file_lock('missing_products'):
            file_path = self._get_file_path('missing_products')
            data = await self._read_json_file(file_path)
            return data.get('articles', {})
    
    async def save_missing_products(self, missing_products: Dict[str, Dict]) -> bool:
        """
        Save missing products data to state file
        
        Args:
            missing_products: Dictionary of missing product information
            
        Returns:
            True if successful
        """
        async with self._file_lock('missing_products'):
            file_path = self._get_file_path('missing_products')
            data = {
                'articles': missing_products,
                'last_updated': datetime.now().isoformat(),
                'total_articles_with_issues': len(missing_products)
            }
            return await self._write_json_file(file_path, data)
    
    async def track_missing_products(self, url: str, visible_shortcodes: List[VisibleShortcode]) -> bool:
        """
        Track missing products for a specific URL
        
        Args:
            url: Article URL
            visible_shortcodes: List of visible shortcodes found
            
        Returns:
            True if successful
        """
        missing_products = await self.load_missing_products()
        
        # Group shortcodes by heading
        headings = {}
        for shortcode in visible_shortcodes:
            if shortcode.heading not in headings:
                headings[shortcode.heading] = {
                    'missing_products': [],
                    'detected_at': datetime.now().isoformat()
                }
            headings[shortcode.heading]['missing_products'].append(shortcode.product_name)
        
        missing_products[url] = {
            'headings': headings,
            'total_missing': len(visible_shortcodes),
            'last_updated': datetime.now().isoformat()
        }
        
        return await self.save_missing_products(missing_products)
    
    async def remove_missing_products(self, url: str, heading: str = None) -> bool:
        """
        Remove missing products tracking for URL or specific heading
        
        Args:
            url: Article URL
            heading: Specific heading to remove (if None, removes entire URL)
            
        Returns:
            True if successful
        """
        missing_products = await self.load_missing_products()
        
        if url not in missing_products:
            return True  # Nothing to remove
        
        if heading is None:
            # Remove entire URL
            del missing_products[url]
        else:
            # Remove specific heading
            if 'headings' in missing_products[url] and heading in missing_products[url]['headings']:
                del missing_products[url]['headings'][heading]
                
                # If no headings left, remove the URL entirely
                if not missing_products[url]['headings']:
                    del missing_products[url]
        
        return await self.save_missing_products(missing_products)
    
    # =============================================================================
    # UTILITY METHODS
    # =============================================================================
    
    async def load_processing_state(self) -> ProcessingState:
        """
        Load complete processing state
        
        Returns:
            ProcessingState object with all current state
        """
        processed_urls = await self.load_processed_urls()
        excluded_urls = await self.load_excluded_urls()
        missing_products = await self.load_missing_products()
        
        return ProcessingState(
            processed_urls=processed_urls,
            excluded_urls=excluded_urls,
            missing_products=missing_products,
            total_processed=len(processed_urls)
        )
    
    async def clear_all_state(self) -> bool:
        """
        Clear all state files (use with caution)
        
        Returns:
            True if successful
        """
        success = True
        
        for file_key in STATE_FILES.keys():
            try:
                file_path = self._get_file_path(file_key)
                if file_path.exists():
                    await asyncio.to_thread(file_path.unlink)
                    self.logger.info(f"Cleared state file: {file_path}")
            except Exception as e:
                self.logger.error(f"Error clearing {file_key}: {e}")
                success = False
        
        return success
    
    async def get_state_summary(self) -> Dict[str, Any]:
        """
        Get summary of current state
        
        Returns:
            Dictionary with state summary information
        """
        state = await self.load_processing_state()
        
        return {
            'processed_urls_count': len(state.processed_urls),
            'excluded_urls_count': len(state.excluded_urls),
            'articles_with_missing_products': len(state.missing_products),
            'total_missing_products': sum(
                len(article_data.get('headings', {})) 
                for article_data in state.missing_products.values()
            ),
            'last_updated': state.last_updated.isoformat(),
            'state_files_exist': {
                file_key: self._get_file_path(file_key).exists()
                for file_key in STATE_FILES.keys()
            }
        }
