#!/usr/bin/env python3
"""
Enhanced Amazon Affiliate Integration Script

This is the main entry point for the enhanced Amazon Affiliate Integration system.
It provides a comprehensive command-line interface with state management, async processing,
and all the enhanced features requested.

Features:
- Async processing with configurable concurrency (4 URLs at a time by default)
- State management with processed URL tracking
- Force parameter to bypass processed URL checks
- Enhanced backup system with Gutenberg block format
- Comprehensive error handling and progress reporting
- Git checkpoint automation
- Domain-specific and cross-domain processing

Usage:
    python enhanced_amazon_affiliate_integration.py --domain example.com
    python enhanced_amazon_affiliate_integration.py --all-domains
    python enhanced_amazon_affiliate_integration.py --domain example.com --force
    python enhanced_amazon_affiliate_integration.py --all-domains --dry-run --limit 5
    python enhanced_amazon_affiliate_integration.py --validate
    python enhanced_amazon_affiliate_integration.py --restore-all
"""

import argparse
import asyncio
import logging
import sys
from typing import Optional, List
import json
from pathlib import Path

# Import the enhanced system components
from amazon_affiliate_integration.orchestration.enhanced_orchestrator import EnhancedOrchestrator
from amazon_affiliate_integration.core.config import (
    CONCURRENCY_LIMIT, get_configured_domains, validate_configuration
)
from amazon_affiliate_integration.utils.helpers import setup_logging
from amazon_affiliate_integration.utils.backup_manager import BackupManager

def setup_argument_parser() -> argparse.ArgumentParser:
    """
    Setup command line argument parser
    
    Returns:
        Configured argument parser
    """
    parser = argparse.ArgumentParser(
        description="Enhanced Amazon Affiliate Integration System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  Process a specific domain:
    python enhanced_amazon_affiliate_integration.py --process --domain majesticmoods.com

  Process all configured domains:
    python enhanced_amazon_affiliate_integration.py --process --all-domains

  Force processing (ignore previously processed URLs):
    python enhanced_amazon_affiliate_integration.py --process --domain example.com --force

  Dry run (don't actually update articles):
    python enhanced_amazon_affiliate_integration.py --process --all-domains --dry-run

  Limit processing for testing:
    python enhanced_amazon_affiliate_integration.py --process --domain example.com --limit 5

  Validate system configuration:
    python enhanced_amazon_affiliate_integration.py --validate

  Restore all articles from backups:
    python enhanced_amazon_affiliate_integration.py --restore-all

  Run complete crawl and cleanup workflow:
    python enhanced_amazon_affiliate_integration.py --crawl-and-cleanup --domain example.com

  Only crawl for visible shortcodes:
    python enhanced_amazon_affiliate_integration.py --crawl-only --all-domains

  Only cleanup failed shortcodes:
    python enhanced_amazon_affiliate_integration.py --cleanup-only --domain example.com

  Use browser crawler for accurate detection:
    python enhanced_amazon_affiliate_integration.py --crawl-only --domain example.com --crawler-method browser

  Use browser crawler with visible browser windows (for debugging):
    python enhanced_amazon_affiliate_integration.py --crawl-only --domain example.com --crawler-method browser --show-browser

  Use hybrid crawler (HTTP first, browser fallback):
    python enhanced_amazon_affiliate_integration.py --crawl-and-cleanup --all-domains --crawler-method hybrid

  Complete workflow (Process → Crawl → Cleanup):
    python enhanced_amazon_affiliate_integration.py --full-workflow --domain example.com
    python enhanced_amazon_affiliate_integration.py --full-workflow --all-domains --force
        """
    )
    
    # Main operation modes
    operation_group = parser.add_mutually_exclusive_group(required=True)
    operation_group.add_argument(
        '--validate',
        action='store_true',
        help='Validate system configuration and connectivity'
    )
    operation_group.add_argument(
        '--restore-all',
        action='store_true',
        help='Restore all articles from backups'
    )
    operation_group.add_argument(
        '--crawl-and-cleanup',
        action='store_true',
        help='Run the complete crawl and cleanup workflow'
    )
    operation_group.add_argument(
        '--crawl-only',
        action='store_true',
        help='Only crawl articles for visible shortcodes (no cleanup)'
    )
    operation_group.add_argument(
        '--cleanup-only',
        action='store_true',
        help='Only cleanup failed shortcodes based on existing data'
    )
    operation_group.add_argument(
        '--process',
        action='store_true',
        help='Process articles (use with --domain or --all-domains)'
    )
    operation_group.add_argument(
        '--full-workflow',
        action='store_true',
        help='Complete workflow: Process articles → Crawl → Cleanup failed shortcodes'
    )

    # Domain selection (for processing and crawler operations)
    parser.add_argument(
        '--domain',
        type=str,
        help='Target a specific domain'
    )
    parser.add_argument(
        '--all-domains',
        action='store_true',
        help='Target all configured domains'
    )
    
    # Processing options
    parser.add_argument(
        '--force',
        action='store_true',
        help='Process all articles regardless of previous processing state'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without actually updating articles'
    )
    parser.add_argument(
        '--limit',
        type=int,
        help='Maximum number of articles to process (for testing)'
    )
    parser.add_argument(
        '--concurrency',
        type=int,
        default=CONCURRENCY_LIMIT,
        help=f'Maximum concurrent processing operations (default: {CONCURRENCY_LIMIT})'
    )

    # Crawler options
    parser.add_argument(
        '--crawler-method',
        choices=['http', 'browser', 'hybrid'],
        default='http',
        help='Crawling method: http (fast, HTTP requests), browser (accurate, real browser), hybrid (HTTP first, browser fallback)'
    )
    parser.add_argument(
        '--show-browser',
        action='store_true',
        help='Show browser windows during crawling (only applies to browser and hybrid methods)'
    )
    
    # Output options
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Suppress non-error output'
    )
    parser.add_argument(
        '--output-json',
        type=str,
        help='Save results to JSON file'
    )
    
    return parser

async def process_domain_command(orchestrator: EnhancedOrchestrator, 
                               domain: str, 
                               force: bool = False,
                               dry_run: bool = False,
                               limit: Optional[int] = None) -> dict:
    """
    Process a specific domain
    
    Args:
        orchestrator: Enhanced orchestrator instance
        domain: Domain to process
        force: Force processing flag
        dry_run: Dry run flag
        limit: Article limit
        
    Returns:
        Processing result dictionary
    """
    logger = logging.getLogger(__name__)
    
    # Validate domain
    configured_domains = get_configured_domains()
    if domain not in configured_domains:
        logger.error(f"Domain '{domain}' is not configured or has no credentials")
        logger.info(f"Available domains: {', '.join(configured_domains)}")
        return {'success': False, 'error': f'Domain {domain} not configured'}
    
    logger.info(f"Starting processing for domain: {domain}")
    
    # Process domain
    result = await orchestrator.process_domain(
        domain=domain,
        force=force,
        dry_run=dry_run,
        limit=limit
    )
    
    # Convert result to dictionary for JSON serialization
    return result.to_dict()

async def process_all_domains_command(orchestrator: EnhancedOrchestrator,
                                    force: bool = False,
                                    dry_run: bool = False,
                                    limit: Optional[int] = None) -> dict:
    """
    Process all configured domains
    
    Args:
        orchestrator: Enhanced orchestrator instance
        force: Force processing flag
        dry_run: Dry run flag
        limit: Article limit per domain
        
    Returns:
        Processing results dictionary
    """
    logger = logging.getLogger(__name__)
    
    configured_domains = get_configured_domains()
    if not configured_domains:
        logger.error("No domains configured with valid credentials")
        return {'success': False, 'error': 'No domains configured'}
    
    logger.info(f"Starting processing for all domains: {', '.join(configured_domains)}")
    
    # Process all domains
    results = await orchestrator.process_all_domains(
        force=force,
        dry_run=dry_run,
        limit=limit
    )
    
    # Convert results to dictionary
    domain_results = [result.to_dict() for result in results]

    # Calculate totals
    total_processed = sum(r['articles_processed'] for r in domain_results)
    total_failed = sum(r['articles_failed'] for r in domain_results)
    total_skipped = sum(r['articles_skipped'] for r in domain_results)
    total_time = sum(r['processing_time'] for r in domain_results)

    return {
        'success': all(r['success'] for r in domain_results),
        'domains_processed': len(configured_domains),
        'total_articles_processed': total_processed,
        'total_articles_failed': total_failed,
        'total_articles_skipped': total_skipped,
        'total_processing_time': total_time,
        'domain_results': domain_results
    }

async def validate_system_command(orchestrator: EnhancedOrchestrator) -> dict:
    """
    Validate system configuration and connectivity
    
    Args:
        orchestrator: Enhanced orchestrator instance
        
    Returns:
        Validation results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting system validation...")
    
    validation_result = await orchestrator.validate_system()
    
    # Log validation results
    if validation_result['overall_status']:
        logger.info("✓ System validation passed")
    else:
        logger.error("✗ System validation failed")
        
        if not validation_result['configuration']['valid']:
            logger.error("Configuration issues:")
            for error in validation_result['configuration']['errors']:
                logger.error(f"  - {error}")
        
        if not validation_result['openai_connection']:
            logger.error("OpenAI connection failed")
        
        for domain, status in validation_result['wordpress_connections'].items():
            if not status:
                logger.error(f"WordPress connection failed for {domain}")
        
        if not validation_result['state_manager']:
            logger.error("State manager test failed")
    
    return validation_result

async def restore_all_command() -> dict:
    """
    Restore all articles from backups
    
    Returns:
        Restoration results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting restore all operation...")
    
    backup_manager = BackupManager()
    
    try:
        # This would need to be implemented in BackupManager
        # For now, return a placeholder
        logger.warning("Restore all functionality not yet implemented")
        return {
            'success': False,
            'error': 'Restore all functionality not yet implemented',
            'message': 'This feature will be implemented in the next phase'
        }
    except Exception as e:
        logger.error(f"Error during restore all operation: {e}")
        return {
            'success': False,
            'error': str(e)
        }

async def crawl_and_cleanup_command(orchestrator: EnhancedOrchestrator,
                                  domain: Optional[str] = None,
                                  crawler_method: str = 'http',
                                  show_browser: bool = False) -> dict:
    """
    Execute the complete crawl and cleanup workflow

    Args:
        orchestrator: Enhanced orchestrator instance
        domain: Specific domain to process (None for all domains)
        crawler_method: Crawling method to use

    Returns:
        Results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting crawl and cleanup workflow...")

    try:
        results = await orchestrator.crawl_and_cleanup_workflow(domain=domain, crawler_method=crawler_method, show_browser=show_browser)

        if results['success']:
            logger.info("Crawl and cleanup workflow completed successfully")
        else:
            logger.error("Crawl and cleanup workflow failed")

        return results

    except Exception as e:
        logger.error(f"Error in crawl and cleanup workflow: {e}")
        return {'success': False, 'error': str(e)}

async def crawl_only_command(orchestrator: EnhancedOrchestrator,
                           domain: Optional[str] = None,
                           crawler_method: str = 'http',
                           show_browser: bool = False) -> dict:
    """
    Execute only the crawling phase

    Args:
        orchestrator: Enhanced orchestrator instance
        domain: Specific domain to process (None for all domains)

    Returns:
        Results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting crawl-only operation...")

    try:
        results = await orchestrator.crawl_and_cleanup_workflow(
            domain=domain,
            crawl_only=True,
            crawler_method=crawler_method,
            show_browser=show_browser
        )

        if results['success']:
            logger.info("Crawl-only operation completed successfully")
        else:
            logger.error("Crawl-only operation failed")

        return results

    except Exception as e:
        logger.error(f"Error in crawl-only operation: {e}")
        return {'success': False, 'error': str(e)}

async def cleanup_only_command(orchestrator: EnhancedOrchestrator,
                             domain: Optional[str] = None) -> dict:
    """
    Execute only the cleanup phase

    Args:
        orchestrator: Enhanced orchestrator instance
        domain: Specific domain to process (None for all domains)

    Returns:
        Results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting cleanup-only operation...")

    try:
        results = await orchestrator.crawl_and_cleanup_workflow(
            domain=domain,
            cleanup_only=True
        )

        if results['success']:
            logger.info("Cleanup-only operation completed successfully")
        else:
            logger.error("Cleanup-only operation failed")

        return results

    except Exception as e:
        logger.error(f"Error in cleanup-only operation: {e}")
        return {'success': False, 'error': str(e)}

async def full_workflow_command(orchestrator: EnhancedOrchestrator,
                              domain: Optional[str] = None,
                              force: bool = False,
                              dry_run: bool = False,
                              limit: Optional[int] = None,
                              crawler_method: str = 'http',
                              show_browser: bool = False) -> dict:
    """
    Execute the complete workflow: Process → Crawl → Cleanup

    Args:
        orchestrator: Enhanced orchestrator instance
        domain: Specific domain to process (None for all domains)
        force: Force reprocessing of previously processed articles
        dry_run: Perform dry run without actually updating articles
        limit: Maximum number of articles to process
        crawler_method: Crawling method to use

    Returns:
        Results dictionary
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting complete workflow: Process → Crawl → Cleanup...")

    try:
        results = await orchestrator.complete_workflow(
            domain=domain,
            force=force,
            dry_run=dry_run,
            limit=limit,
            crawler_method=crawler_method,
            show_browser=show_browser
        )

        if results['success']:
            logger.info("Complete workflow finished successfully")
        else:
            logger.error("Complete workflow failed")

        return results

    except Exception as e:
        logger.error(f"Error in complete workflow: {e}")
        return {'success': False, 'error': str(e)}

def save_results_to_json(results: dict, output_file: str):
    """
    Save results to JSON file
    
    Args:
        results: Results dictionary
        output_file: Output file path
    """
    try:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"Results saved to: {output_path}")
    except Exception as e:
        print(f"Error saving results to JSON: {e}")

async def main():
    """Main entry point"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.WARNING if args.quiet else logging.INFO
    logger = setup_logging(log_level=logging.getLevelName(log_level))
    
    try:
        # Initialize orchestrator
        orchestrator = EnhancedOrchestrator(concurrency_limit=args.concurrency)
        
        # Execute command
        if args.validate:
            results = await validate_system_command(orchestrator)
        elif args.restore_all:
            results = await restore_all_command()
        elif args.crawl_and_cleanup:
            domain = args.domain if args.domain else None
            results = await crawl_and_cleanup_command(orchestrator, domain, args.crawler_method, args.show_browser)
        elif args.crawl_only:
            domain = args.domain if args.domain else None
            results = await crawl_only_command(orchestrator, domain, args.crawler_method, args.show_browser)
        elif args.cleanup_only:
            domain = args.domain if args.domain else None
            results = await cleanup_only_command(orchestrator, domain)
        elif args.full_workflow:
            domain = args.domain if args.domain else None
            results = await full_workflow_command(
                orchestrator, domain, args.force, args.dry_run, args.limit, args.crawler_method, args.show_browser
            )
        elif args.process:
            if args.domain:
                results = await process_domain_command(
                    orchestrator, args.domain, args.force, args.dry_run, args.limit
                )
            elif args.all_domains:
                results = await process_all_domains_command(
                    orchestrator, args.force, args.dry_run, args.limit
                )
            else:
                print("Error: --process requires either --domain or --all-domains")
                parser.print_help()
                return 1
        else:
            parser.print_help()
            return 1
        
        # Save results to JSON if requested
        if args.output_json:
            save_results_to_json(results, args.output_json)
        
        # Print summary
        if not args.quiet:
            if results.get('success', False):
                print("✓ Operation completed successfully")
            else:
                print("✗ Operation failed")
                if 'error' in results:
                    print(f"Error: {results['error']}")
        
        return 0 if results.get('success', False) else 1
        
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
